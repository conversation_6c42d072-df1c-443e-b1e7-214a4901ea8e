#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股数据获取与聚宽实盘交易完整指南
FinRL + JoinQuant 集成方案

作者: FinRL用户指南
日期: 2025-01-05
"""

import pandas as pd
import numpy as np
import jqdatasdk as jq
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# FinRL相关导入
from finrl.meta.preprocessor.preprocessors import FeatureEngineer, data_split
from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
from finrl.config import INDICATORS

class AStockDataProcessor:
    """A股数据处理器 - 基于聚宽JQData"""
    
    def __init__(self, username, password):
        """
        初始化聚宽数据连接
        
        Args:
            username: 聚宽账号
            password: 聚宽密码
        """
        self.username = username
        self.password = password
        self.auth_status = False
        
    def authenticate(self):
        """聚宽账号认证"""
        try:
            jq.auth(self.username, self.password)
            self.auth_status = True
            print("✅ 聚宽账号认证成功")
            
            # 查询账户信息
            account_info = jq.get_query_count()
            print(f"📊 今日剩余查询次数: {account_info['spare']}")
            return True
            
        except Exception as e:
            print(f"❌ 聚宽账号认证失败: {e}")
            return False
    
    def get_stock_list(self, market='all'):
        """
        获取股票列表
        
        Args:
            market: 'all', 'sz', 'sh' - 全部/深圳/上海
        """
        if not self.auth_status:
            self.authenticate()
            
        try:
            if market == 'all':
                stocks = jq.get_all_securities(['stock'])
            elif market == 'sz':
                stocks = jq.get_all_securities(['stock'])
                stocks = stocks[stocks.index.str.contains('XSHE')]
            elif market == 'sh':
                stocks = jq.get_all_securities(['stock'])
                stocks = stocks[stocks.index.str.contains('XSHG')]
            
            print(f"📈 获取到 {len(stocks)} 只股票")
            return stocks.index.tolist()
            
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return []
    
    def fetch_stock_data(self, stock_list, start_date, end_date, fields=None):
        """
        获取股票历史数据
        
        Args:
            stock_list: 股票代码列表 ['000001.XSHE', '000002.XSHE']
            start_date: 开始日期 '2020-01-01'
            end_date: 结束日期 '2023-12-31'
            fields: 数据字段 ['open', 'close', 'high', 'low', 'volume']
        """
        if not self.auth_status:
            self.authenticate()
            
        if fields is None:
            fields = ['open', 'close', 'high', 'low', 'volume', 'money']
        
        try:
            print(f"🔄 正在获取 {len(stock_list)} 只股票的数据...")
            
            # 使用聚宽get_price接口批量获取数据
            data = jq.get_price(
                security=stock_list,
                start_date=start_date,
                end_date=end_date,
                frequency='daily',
                fields=fields,
                skip_paused=False,
                fq='pre'  # 前复权
            )
            
            # 转换为FinRL格式
            df_list = []
            for stock in stock_list:
                stock_data = data.loc[stock].copy()
                stock_data['tic'] = stock
                stock_data['date'] = stock_data.index
                stock_data = stock_data.reset_index(drop=True)
                
                # 重命名列以符合FinRL格式
                stock_data = stock_data.rename(columns={
                    'money': 'amount'
                })
                
                df_list.append(stock_data)
            
            # 合并所有股票数据
            final_df = pd.concat(df_list, ignore_index=True)
            final_df = final_df.sort_values(['date', 'tic']).reset_index(drop=True)
            
            print(f"✅ 成功获取数据，共 {len(final_df)} 条记录")
            return final_df
            
        except Exception as e:
            print(f"❌ 获取股票数据失败: {e}")
            return pd.DataFrame()
    
    def add_technical_indicators(self, df, indicators=None):
        """添加技术指标"""
        if indicators is None:
            indicators = INDICATORS
            
        try:
            fe = FeatureEngineer(
                use_technical_indicator=True,
                tech_indicator_list=indicators,
                use_vix=False,
                use_turbulence=False,
                user_defined_feature=False
            )
            
            processed_df = fe.preprocess_data(df)
            print(f"✅ 成功添加技术指标: {indicators}")
            return processed_df
            
        except Exception as e:
            print(f"❌ 添加技术指标失败: {e}")
            return df

class JoinQuantTrader:
    """聚宽实盘交易接口"""
    
    def __init__(self, username, password):
        """
        初始化聚宽交易接口
        
        注意: 聚宽实盘交易需要:
        1. 开通聚宽实盘交易权限
        2. 绑定券商账户
        3. 使用聚宽提供的交易API
        """
        self.username = username
        self.password = password
        self.authenticated = False
        
    def authenticate(self):
        """认证聚宽账户"""
        try:
            jq.auth(self.username, self.password)
            self.authenticated = True
            print("✅ 聚宽交易账户认证成功")
            return True
        except Exception as e:
            print(f"❌ 聚宽交易账户认证失败: {e}")
            return False
    
    def get_account_info(self):
        """获取账户信息"""
        if not self.authenticated:
            self.authenticate()
            
        # 注意: 这里需要使用聚宽的实盘交易API
        # 具体API需要参考聚宽官方文档
        print("📊 获取账户信息...")
        print("⚠️  实盘交易需要聚宽实盘权限和券商绑定")
        
    def place_order(self, stock_code, quantity, side='buy', order_type='market'):
        """
        下单交易
        
        Args:
            stock_code: 股票代码
            quantity: 交易数量
            side: 'buy' 或 'sell'
            order_type: 'market' 或 'limit'
        """
        if not self.authenticated:
            self.authenticate()
            
        print(f"📝 模拟下单: {side} {quantity} 股 {stock_code}")
        print("⚠️  实际下单需要聚宽实盘API接口")
        
        # 实际实现需要调用聚宽的实盘交易API
        # 例如: jq.order(stock_code, quantity) 等
        
def create_finrl_environment(df, initial_amount=1000000):
    """创建FinRL交易环境"""
    
    # 数据分割
    train_data = data_split(df, '2020-01-01', '2022-12-31')
    trade_data = data_split(df, '2023-01-01', '2023-12-31')
    
    # 创建交易环境
    stock_dimension = len(df.tic.unique())
    state_space = 1 + 2*stock_dimension + len(INDICATORS)*stock_dimension
    
    env_kwargs = {
        "hmax": 100,
        "initial_amount": initial_amount,
        "num_stock_shares": [0] * stock_dimension,
        "buy_cost_pct": [0.001] * stock_dimension,
        "sell_cost_pct": [0.001] * stock_dimension,
        "state_space": state_space,
        "stock_dim": stock_dimension,
        "tech_indicator_list": INDICATORS,
        "action_space": stock_dimension,
        "reward_scaling": 1e-4
    }
    
    env_train = StockTradingEnv(df=train_data, **env_kwargs)
    env_trade = StockTradingEnv(df=trade_data, **env_kwargs)
    
    return env_train, env_trade

def main_example():
    """完整示例流程"""
    
    print("🚀 A股量化交易完整流程示例")
    print("="*50)
    
    # 1. 初始化数据处理器
    username = "your_joinquant_username"  # 替换为您的聚宽账号
    password = "your_joinquant_password"  # 替换为您的聚宽密码
    
    processor = AStockDataProcessor(username, password)
    
    # 2. 认证并获取数据
    if processor.authenticate():
        
        # 选择股票池 - 示例选择几只活跃股票
        stock_list = ['000001.XSHE', '000002.XSHE', '600000.XSHG', '600036.XSHG']
        
        # 获取历史数据
        df = processor.fetch_stock_data(
            stock_list=stock_list,
            start_date='2020-01-01',
            end_date='2023-12-31'
        )
        
        if not df.empty:
            # 3. 添加技术指标
            df_processed = processor.add_technical_indicators(df)
            
            # 4. 创建FinRL环境
            env_train, env_trade = create_finrl_environment(df_processed)
            
            print("✅ FinRL环境创建成功")
            print(f"📊 训练环境状态空间: {env_train.state_space}")
            print(f"📊 交易环境动作空间: {env_trade.action_space}")
            
            # 5. 初始化交易接口
            trader = JoinQuantTrader(username, password)
            trader.get_account_info()
            
            print("\n🎯 下一步:")
            print("1. 使用FinRL训练强化学习模型")
            print("2. 在聚宽平台配置实盘交易")
            print("3. 部署策略到实盘环境")
            
        else:
            print("❌ 数据获取失败，请检查网络和账户权限")
    
    else:
        print("❌ 聚宽账户认证失败，请检查用户名和密码")

if __name__ == "__main__":
    main_example()
