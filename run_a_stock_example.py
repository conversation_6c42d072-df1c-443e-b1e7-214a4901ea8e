#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股量化交易实战示例
使用FinRL + 聚宽JQData的完整流程

运行前请确保:
1. 已安装 finrl 和 jqdatasdk
2. 已注册聚宽账户并获得JQData权限
3. 修改下方的用户名和密码
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def main():
    """主函数 - 完整的A股量化交易流程"""
    
    print("🚀 A股量化交易实战示例")
    print("=" * 60)
    
    # ==================== 配置参数 ====================
    
    # 聚宽账户信息 - 请替换为您的真实账户
    JOINQUANT_USERNAME = "your_username_here"  # 替换为您的聚宽用户名
    JOINQUANT_PASSWORD = "your_password_here"  # 替换为您的聚宽密码
    
    # 交易参数
    INITIAL_AMOUNT = 1000000  # 初始资金100万
    START_DATE = '2020-01-01'
    END_DATE = '2023-12-31'
    TRAIN_END_DATE = '2022-12-31'
    TEST_START_DATE = '2023-01-01'
    
    # 股票池 - 选择一些活跃的大盘股
    STOCK_LIST = [
        '000001.XSHE',  # 平安银行
        '000002.XSHE',  # 万科A
        '600000.XSHG',  # 浦发银行
        '600036.XSHG',  # 招商银行
        '600519.XSHG',  # 贵州茅台
    ]
    
    # 技术指标
    TECH_INDICATORS = [
        'macd', 'boll_ub', 'boll_lb', 'rsi_30', 
        'cci_30', 'dx_30', 'close_30_sma', 'close_60_sma'
    ]
    
    # ==================== 检查账户信息 ====================
    
    if JOINQUANT_USERNAME == "your_username_here":
        print("❌ 请先配置您的聚宽账户信息!")
        print("   修改 JOINQUANT_USERNAME 和 JOINQUANT_PASSWORD")
        return
    
    # ==================== 数据获取 ====================
    
    print("\n📊 步骤1: 获取A股数据")
    print("-" * 40)
    
    try:
        import jqdatasdk as jq
        
        # 认证聚宽账户
        print(f"🔐 正在认证聚宽账户: {JOINQUANT_USERNAME}")
        jq.auth(JOINQUANT_USERNAME, JOINQUANT_PASSWORD)
        
        # 检查查询次数
        query_count = jq.get_query_count()
        print(f"📈 今日剩余查询次数: {query_count['spare']}")
        
        if query_count['spare'] < 10:
            print("⚠️  查询次数不足，建议明天再试或升级账户")
            return
        
        # 获取股票数据
        print(f"📥 正在获取 {len(STOCK_LIST)} 只股票的历史数据...")
        
        raw_data = jq.get_price(
            STOCK_LIST,
            start_date=START_DATE,
            end_date=END_DATE,
            frequency='daily',
            fields=['open', 'close', 'high', 'low', 'volume', 'money'],
            skip_paused=False,
            fq='pre'  # 前复权
        )
        
        print(f"✅ 数据获取成功! 数据形状: {raw_data.shape}")
        
    except ImportError:
        print("❌ 请先安装 jqdatasdk: pip install jqdatasdk")
        return
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        print("   请检查网络连接和账户权限")
        return
    
    # ==================== 数据预处理 ====================
    
    print("\n🔧 步骤2: 数据预处理")
    print("-" * 40)
    
    # 转换为FinRL格式
    df_list = []
    for stock in STOCK_LIST:
        stock_data = raw_data.loc[stock].copy()
        stock_data['tic'] = stock
        stock_data['date'] = stock_data.index
        stock_data = stock_data.reset_index(drop=True)
        
        # 重命名列
        stock_data = stock_data.rename(columns={'money': 'amount'})
        df_list.append(stock_data)
    
    # 合并数据
    df = pd.concat(df_list, ignore_index=True)
    df = df.sort_values(['date', 'tic']).reset_index(drop=True)
    
    print(f"✅ 数据格式转换完成! 最终数据形状: {df.shape}")
    print(f"📅 数据时间范围: {df['date'].min()} 到 {df['date'].max()}")
    
    # 添加技术指标
    try:
        from finrl.meta.preprocessor.preprocessors import FeatureEngineer
        
        fe = FeatureEngineer(
            use_technical_indicator=True,
            tech_indicator_list=TECH_INDICATORS,
            use_vix=False,
            use_turbulence=False,
            user_defined_feature=False
        )
        
        df_processed = fe.preprocess_data(df)
        print(f"✅ 技术指标添加完成! 特征数量: {len(df_processed.columns)}")
        
    except ImportError:
        print("❌ 请先安装 finrl: pip install finrl")
        return
    except Exception as e:
        print(f"❌ 技术指标计算失败: {e}")
        df_processed = df
    
    # ==================== 创建交易环境 ====================
    
    print("\n🏗️ 步骤3: 创建FinRL交易环境")
    print("-" * 40)
    
    try:
        from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
        from finrl.meta.preprocessor.preprocessors import data_split
        
        # 数据分割
        train_data = data_split(df_processed, START_DATE, TRAIN_END_DATE)
        test_data = data_split(df_processed, TEST_START_DATE, END_DATE)
        
        print(f"📊 训练数据: {len(train_data)} 条记录")
        print(f"📊 测试数据: {len(test_data)} 条记录")
        
        # 环境参数
        stock_dimension = len(STOCK_LIST)
        state_space = 1 + 2*stock_dimension + len(TECH_INDICATORS)*stock_dimension
        
        env_kwargs = {
            "hmax": 100,
            "initial_amount": INITIAL_AMOUNT,
            "num_stock_shares": [0] * stock_dimension,
            "buy_cost_pct": [0.001] * stock_dimension,
            "sell_cost_pct": [0.001] * stock_dimension,
            "state_space": state_space,
            "stock_dim": stock_dimension,
            "tech_indicator_list": TECH_INDICATORS,
            "action_space": stock_dimension,
            "reward_scaling": 1e-4
        }
        
        # 创建环境
        env_train = StockTradingEnv(df=train_data, **env_kwargs)
        env_test = StockTradingEnv(df=test_data, **env_kwargs)
        
        print(f"✅ 交易环境创建成功!")
        print(f"📊 状态空间维度: {state_space}")
        print(f"📊 动作空间维度: {stock_dimension}")
        
    except Exception as e:
        print(f"❌ 环境创建失败: {e}")
        return
    
    # ==================== 模型训练 ====================
    
    print("\n🤖 步骤4: 强化学习模型训练")
    print("-" * 40)
    
    try:
        from stable_baselines3 import PPO
        from stable_baselines3.common.vec_env import DummyVecEnv
        
        # 创建向量化环境
        env_train_vec = DummyVecEnv([lambda: env_train])
        
        # 创建PPO模型
        model = PPO(
            "MlpPolicy",
            env_train_vec,
            verbose=1,
            learning_rate=0.0003,
            n_steps=2048,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            tensorboard_log="./ppo_a_stock_tensorboard/"
        )
        
        print("🔄 开始训练模型 (这可能需要几分钟)...")
        
        # 训练模型
        model.learn(total_timesteps=50000)
        
        # 保存模型
        model.save("ppo_a_stock_model")
        print("✅ 模型训练完成并已保存!")
        
    except ImportError:
        print("❌ 请先安装 stable-baselines3: pip install stable-baselines3")
        return
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        return
    
    # ==================== 回测评估 ====================
    
    print("\n📈 步骤5: 策略回测")
    print("-" * 40)
    
    try:
        # 在测试环境中运行策略
        obs = env_test.reset()
        portfolio_values = []
        
        for i in range(len(test_data) // stock_dimension):
            action, _states = model.predict(obs, deterministic=True)
            obs, rewards, done, info = env_test.step(action)
            
            if 'total_asset' in info:
                portfolio_values.append(info['total_asset'])
            
            if done:
                break
        
        # 计算基准收益 (买入持有策略)
        benchmark_returns = []
        for stock in STOCK_LIST:
            stock_data = test_data[test_data['tic'] == stock]
            if len(stock_data) > 0:
                stock_return = (stock_data['close'].iloc[-1] / stock_data['close'].iloc[0] - 1)
                benchmark_returns.append(stock_return)
        
        avg_benchmark_return = np.mean(benchmark_returns)
        
        # 计算策略收益
        if portfolio_values:
            strategy_return = (portfolio_values[-1] / INITIAL_AMOUNT - 1)
            
            print(f"📊 回测结果:")
            print(f"   策略总收益率: {strategy_return:.2%}")
            print(f"   基准收益率: {avg_benchmark_return:.2%}")
            print(f"   超额收益: {(strategy_return - avg_benchmark_return):.2%}")
            print(f"   最终资产: ¥{portfolio_values[-1]:,.2f}")
        
        # 绘制收益曲线
        if portfolio_values:
            plt.figure(figsize=(12, 6))
            plt.plot(portfolio_values, label='强化学习策略', linewidth=2)
            plt.axhline(y=INITIAL_AMOUNT, color='r', linestyle='--', label='初始资金')
            plt.title('A股量化交易策略回测结果')
            plt.xlabel('交易日')
            plt.ylabel('资产价值 (¥)')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig('a_stock_backtest_result.png', dpi=300, bbox_inches='tight')
            plt.show()
            
            print("✅ 回测图表已保存为 'a_stock_backtest_result.png'")
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
    
    # ==================== 实盘交易提示 ====================
    
    print("\n💼 步骤6: 实盘交易部署")
    print("-" * 40)
    print("🎯 实盘交易需要:")
    print("   1. 申请聚宽实盘交易权限")
    print("   2. 绑定券商账户 (华泰、中信、招商等)")
    print("   3. 完成风险评估和资金要求")
    print("   4. 使用聚宽实盘交易API")
    print("\n📞 联系方式:")
    print("   聚宽客服: https://www.joinquant.com/")
    print("   实盘申请: 登录聚宽平台查看实盘交易选项")
    
    print("\n🎉 A股量化交易流程演示完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
